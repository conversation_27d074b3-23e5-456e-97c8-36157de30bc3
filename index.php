<?php include_once '../lib/config.php';
if(SITE_WORKING_STATUS){
    echo '<center style="position: relative; top: 100px;"><h1>This site is under maintenance</h1></center>';die;
}
if(isset($_GET['ref']) && !empty($_GET['ref'])){$uid = $_GET['ref'];}
elseif(isset($_SESSION['userid']) && !empty($_SESSION['userid'])){$uid = $_SESSION['userid'];}
else{$uid = '0';}
//redirect('../register.php?r='.$uid);die;
?>
<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
        <title><?php echo $title_name = isset($title) ? SITE_NAME . ' | ' . $title : SITE_NAME . ' | Member Register';?></title>
        <link rel="shortcut icon" href="images/nexabot-logo.png" type="image/x-icon">
        <script src="https://ajax.googleapis.com/ajax/libs/webfont/1.6.26/webfont.js"></script>
        <script>
            WebFont.load({
                google: {
                    families: ['Alegreya+Sans:100,100i,300,300i,400,400i,500,500i,700,700i,800,800i,900,900i', 'Raleway:100,100i,200,200i,300,300i,400,400i,500,500i,600,600i,700,700i,800,800i,900,900i', 'Open Sans']
                }
            });
        </script>
        <!-- Bootstrap -->
        <link href="../assets/bootstrap/css/bootstrap.min.css" rel="stylesheet" type="text/css"/>
        <!-- Bootstrap rtl -->
        <!--<link href="../assets/bootstrap-rtl/bootstrap-rtl.min.css" rel="stylesheet" type="text/css"/>-->
        <!-- Pe-icon-7-stroke -->
        <link href="../assets/pe-icon-7-stroke/css/pe-icon-7-stroke.css" rel="stylesheet" type="text/css"/>
        <link href="../assets/font-awesome/css/font-awesome.min.css" rel="stylesheet" type="text/css"/>
        <!-- Theme style -->
        <link href="../assets/dist/css/component_ui.min.css" rel="stylesheet" type="text/css"/>
        <link href="../assets/dist/css/skins/component_ui_black.css" rel="stylesheet" type="text/css"/>
        <!-- Theme style rtl -->
        <!--<link href="../assets/dist/css/component_ui_rtl.css" rel="stylesheet" type="text/css"/>-->
        <!-- Custom css -->
        <link href="../assets/dist/css/custom.css" rel="stylesheet" type="text/css"/>
        <style>
            /* Base Styles - Dark Theme */
            body {
                background: #1a1f2e;
                color: #e6edf3;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
                -webkit-font-smoothing: antialiased;
                -moz-osx-font-smoothing: grayscale;
                min-height: 100vh;
                margin: 0;
                padding: 20px;
                display: flex;
                align-items: center;
                justify-content: center;
            }

            .main-wrapper {
                width: 50%;
                max-width: 1200px;
                height: 90vh;
                background: #ffffff;
                border-radius: 16px;
                overflow: hidden;
                box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            }

            .main-container {
                display: flex;
                height: 100%;
            }

            /* Left Sidebar */
            .sidebar {
                flex: 1;
                background: linear-gradient(135deg, #00cc88 0%, #0099ff 100%);
                padding: 30px;
                display: flex;
                flex-direction: column;
                justify-content: space-between;
                position: relative;
                overflow: hidden;
            }

            .sidebar-content {
                z-index: 2;
                position: relative;
                flex: 1;
                display: flex;
                flex-direction: column;
                justify-content: center;
            }

            .feature-item {
                display: flex;
                align-items: center;
                margin-bottom: 25px;
                color: white;
            }

            .feature-icon {
                width: 35px;
                height: 35px;
                background: rgba(255, 255, 255, 0.2);
                border-radius: 8px;
                display: flex;
                align-items: center;
                justify-content: center;
                margin-right: 12px;
                font-size: 16px;
            }

            .feature-text {
                font-size: 15px;
                font-weight: 500;
                line-height: 1.3;
            }

            /* Coin Graphics */
            .coins-container {
                position: relative;
                bottom: 0;
                left: 0;
                right: 0;
                height: auto;
                display: flex;
                align-items: center;
                justify-content: center;
                margin-top: 20px;
                z-index: 2;
            }

            .coins-image {
                max-width: 100%;
                max-height: 150px;
                object-fit: contain;
                opacity: 0.9;
            }

            /* Crystal/Diamond */
            .crystal {
                position: absolute;
                top: 50%;
                right: 60px;
                width: 40px;
                height: 40px;
                background: linear-gradient(45deg, #00d4aa, #00b4d8);
                transform: rotate(45deg) translateY(-50%);
                border-radius: 8px;
                box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
            }

            /* Form Container */
            .form-container {
                flex: 1;
                background-color: #1a1f2e;
                padding: 30px;
                display: flex;
                flex-direction: column;
                justify-content: center;
                max-width: 500px;
                overflow-y: auto;
            }

            /* Form Title */
            .form-title {
                font-size: 26px;
                font-weight: 600;
                margin-bottom: 25px;
                color: #ffffff;
                text-align: left;
            }

            /* Form Group */
            .form-group {
                margin-bottom: 16px;
                position: relative;
            }

            /* Form Label */
            .form-label {
                display: block;
                font-size: 14px;
                color: #94a3b8;
                margin-bottom: 8px;
            }

            .required::after {
                content: " *";
                color: #ef4444;
            }

            /* Form Control */
            .form-control {
                width: 100%;
                background-color: #2a3441;
                color: #ffffff;
                border: 1px solid #3a4553;
                border-radius: 8px;
                padding: 12px 15px 12px 40px;
                font-size: 14px;
                box-sizing: border-box;
                transition: all 0.3s ease;
            }

            .form-control:focus {
                outline: none;
                border-color: #00cc88;
                box-shadow: 0 0 0 2px rgba(0, 204, 136, 0.2);
            }

            /* Form Icon */
            .form-icon {
                position: absolute;
                left: 15px;
                top: 36px;
                color: #64748b;
                font-size: 14px;
            }

            /* Select Control */
            .select-control {
                appearance: none;
                background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='%2364748b' viewBox='0 0 16 16'%3E%3Cpath d='M7.247 11.14 2.451 5.658C1.885 5.013 2.345 4 3.204 4h9.592a1 1 0 0 1 .753 1.659l-4.796 5.48a1 1 0 0 1-1.506 0z'/%3E%3C/svg%3E");
                background-repeat: no-repeat;
                background-position: right 15px center;
                padding-right: 40px;
            }

            /* Checkbox */
            .form-check {
                display: flex;
                align-items: flex-start;
                margin-bottom: 20px;
            }

            .form-check-input {
                width: 16px;
                height: 16px;
                margin-right: 8px;
                margin-top: 2px;
                accent-color: #00cc88;
            }

            .form-check-label {
                font-size: 13px;
                color: #94a3b8;
                line-height: 1.3;
            }

            /* Button */
            .btn-register {
                display: flex;
                align-items: center;
                justify-content: center;
                width: 100%;
                background: linear-gradient(90deg, #00cc88, #0099ff);
                color: #ffffff;
                border: none;
                border-radius: 8px;
                padding: 12px;
                font-size: 15px;
                font-weight: 500;
                cursor: pointer;
                text-align: center;
                margin-bottom: 15px;
                transition: all 0.3s ease;
            }

            .btn-register:hover {
                background: linear-gradient(90deg, #00b377, #0088ee);
                transform: translateY(-1px);
            }

            .btn-register:disabled {
                background: #4a5568;
                cursor: not-allowed;
                transform: none;
            }

            .btn-register i {
                margin-left: 8px;
            }

            /* Links */
            a {
                color: #00cc88;
                text-decoration: none;
            }

            a:hover {
                text-decoration: underline;
            }

            /* Login Link */
            .login-link {
                text-align: center;
                font-size: 13px;
                color: #94a3b8;
                margin-top: 15px;
            }

            /* Error Messages */
            .error-message {
                color: #ef4444;
                font-size: 13px;
                margin-top: 5px;
            }

            .success-message {
                color: #00cc88;
                font-size: 13px;
                margin-top: 5px;
            }

            /* Notification */
            .notification {
                background-color: #2a3441;
                border-left: 4px solid #00cc88;
                color: #e6edf3;
                padding: 12px;
                margin-bottom: 15px;
                border-radius: 6px;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            }

            .notification.error {
                border-left-color: #ef4444;
            }

            .notification.warning {
                border-left-color: #f59e0b;
            }

            /* Fix for select dropdown */
            select.form-control {
                color: #ffffff;
            }

            select.form-control option {
                background-color: #2a3441;
                color: #ffffff;
            }

            /* Responsive */
            @media (max-width: 768px) {
                body {
                    padding: 10px;
                }

                .main-wrapper {
                    max-width: 100%;
                    height: 95vh;
                }

                .main-container {
                    flex-direction: column;
                }

                .sidebar {
                    min-height: 250px;
                    padding: 20px;
                }

                .form-container {
                    padding: 20px;
                    flex: 1;
                }

                .feature-item {
                    margin-bottom: 15px;
                }

                .feature-text {
                    font-size: 14px;
                }

                .coins-container {
                    margin-top: 10px;
                }

                .coins-image {
                    max-height: 80px;
                }

                .crystal {
                    display: none;
                }

                .form-title {
                    font-size: 22px;
                    margin-bottom: 20px;
                }

                .form-group {
                    margin-bottom: 14px;
                }
            }

            /* Country Dropdown Styling */
            .country-dropdown {
                position: absolute;
                width: 100%;
                max-height: 200px;
                overflow-y: auto;
                background: #2a3441;
                border: 1px solid #3a4553;
                border-radius: 8px;
                z-index: 1000;
                display: none;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
                top: calc(100% + 5px);
                left: 0;
            }

            .country-dropdown-item {
                padding: 10px 15px;
                cursor: pointer;
                color: #e6edf3;
                transition: all 0.2s ease;
                border-bottom: 1px solid rgba(255, 255, 255, 0.05);
            }

            .country-dropdown-item:hover, .country-dropdown-item.active {
                background: #3a4553;
                color: #00cc88;
            }

            .country-dropdown-item:last-child {
                border-bottom: none;
            }

            #no-results {
                padding: 10px 15px;
                color: #94a3b8;
                font-style: italic;
                text-align: center;
            }

            .country-error {
                font-size: 13px;
                margin-top: 5px;
                display: block;
                color: #ef4444;
            }

            .country-success {
                font-size: 13px;
                margin-top: 5px;
                display: block;
                color: #00cc88;
            }
        </style>
    </head>
    <body>
        <div class="main-wrapper">
            <div class="main-container">
            <!-- Left Sidebar -->
            <div class="sidebar">
                <div class="sidebar-content">
                    <div class="feature-item">
                        <div class="feature-icon">
                            <i class="fa fa-gamepad"></i>
                        </div>
                        <div class="feature-text">
                            Exciting and fair games<br>and lotteries
                        </div>
                    </div>

                    <div class="feature-item">
                        <div class="feature-icon">
                            <i class="fa fa-line-chart"></i>
                        </div>
                        <div class="feature-text">
                            Staking offers with<br>returns of up to 200%
                        </div>
                    </div>

                    <div class="feature-item">
                        <div class="feature-icon">
                            <i class="fa fa-shield"></i>
                        </div>
                        <div class="feature-text">
                            We ensure your security<br>and anonymity
                        </div>
                    </div>

                    <div class="feature-item">
                        <div class="feature-icon">
                            <i class="fa fa-users"></i>
                        </div>
                        <div class="feature-text">
                            Join our generous<br>affiliate program
                        </div>
                    </div>
                </div>

                <!-- Coin Graphics -->
                <div class="coins-container">
                    <img src="https://i.ibb.co/pNQMJ43/coins.png" alt="Coins" class="coins-image">
                </div>

                <!-- Crystal -->
                <div class="crystal"></div>
            </div>

            <!-- Right Form Container -->
            <div class="form-container">
                <h1 class="form-title">Create an account</h1>

                <?php if(isset($_SESSION['SetMessage'])): ?>
                <div class="notification <?php echo strpos($_SESSION['SetMessage'], 'danger') !== false ? 'error' : 'success'; ?>">
                    <?php echo getMessage(); ?>
                </div>
                <?php endif; ?>
                
                <form action="register_model.php" id="loginForm" method="post">
                    <div class="form-group">
                        <label for="refer_id" class="form-label required">Referral id</label>
                        <i class="form-icon fa fa-user"></i>
                        <input type="text" class="form-control" id="refer_id" name="refer_id" value="<?php echo get_user_details($uid)->login_id;?>" maxlength="20" required="required" onBlur="check_sponser(this.value);" />
                        <span id="sponser" class="error-message"></span>
                    </div>
                    
                    <div class="form-group">
                        <label for="position" class="form-label required">Select Position</label>
                        <i class="form-icon fa fa-map-marker"></i>
                        <select name="position" id="position" class="form-control select-control" required="required">
                            <option value="" disabled="disabled" selected="selected">Select position</option>
                            <option value="L" <?php if((isset($_SESSION['position']) && $_SESSION['position']=='L') || (isset($_GET['p']) && $_GET['p']=='L')){echo "selected='selected'";}elseif(isset($_SESSION['position']) && $_SESSION['position']=='R'){echo "disabled='disabled'";}?>>Left</option>
                            <option value="R" <?php if((isset($_SESSION['position']) && $_SESSION['position']=='R') || (isset($_GET['p']) && $_GET['p']=='R')){echo "selected='selected'";}elseif(isset($_SESSION['position']) && $_SESSION['position']=='L'){echo "disabled='disabled'";}?>>Right</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="name" class="form-label required">Name</label>
                        <i class="form-icon fa fa-user"></i>
                        <input type="text" class="form-control" id="name" name="name" maxlength="50" required="required" pattern="[a-zA-Z ]+" />
                    </div>
                    
                    <div class="form-group">
                        <label for="email" class="form-label required">Email</label>
                        <i class="form-icon fa fa-envelope"></i>
                        <input type="email" class="form-control" id="email" name="email" maxlength="100" required="required" onBlur="check_email(this.value);" />
                        <span id="email_error" class="error-message"></span>
                    </div>
                    
                    <div class="form-group">
                        <label for="mobile" class="form-label required">Contact</label>
                        <i class="form-icon fa fa-phone"></i>
                        <input type="text" class="form-control" id="mobile" name="mobile" maxlength="10" required="required" onBlur="check_mobile(this.value);" pattern="[0-9]{10,10}" />
                        <span id="mobile_error" class="error-message"></span>
                    </div>
                    
                    <div class="form-group">
                        <label for="password" class="form-label required">Password</label>
                        <i class="form-icon fa fa-lock"></i>
                        <input type="password" class="form-control" id="password" name="password" maxlength="20" required="required" onchange="form.confirm_password.pattern = this.value;" />
                    </div>
                    
                    <div class="form-group">
                        <label for="confirm_password" class="form-label required">Confirm Password</label>
                        <i class="form-icon fa fa-lock"></i>
                        <input type="password" class="form-control" id="confirm_password" name="confirm_password" maxlength="20" required="required" pattern="" />
                        <span id="password_error" class="error-message"></span>
                    </div>
                    
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="flexCheckDefault" required="required">
                        <label class="form-check-label" for="flexCheckDefault">
                            I have read and accept the terms of the <a href="../../terms.pdf" target="_blank">User Agreement</a> and <a href="#">Privacy Policy</a>.
                        </label>
                    </div>
                    
                    <button class="btn-register" type="submit" id="submit">
                        Register Now
                        <i class="fa fa-arrow-right"></i>
                    </button>

                    <div class="login-link">
                        Already have an account? <a href="index.php">Sign in</a>
                    </div>
                </form>
            </div>
            </div>
        </div>

        <!-- jQuery -->
        <script src="../assets/plugins/jQuery/jquery-1.12.4.min.js"></script>
        <script>
            // Initialize submit button as disabled
            $(document).ready(function(){
                $("#submit").attr("disabled", "true");
                
                // Password confirmation validation
                $("#confirm_password").on("input", function() {
                    if($(this).val() !== $("#password").val()) {
                        $("#password_error").html("Passwords do not match").addClass("error-message").removeClass("success-message");
                        $("#submit").attr("disabled", "true");
                    } else {
                        $("#password_error").html("").removeClass("error-message");
                        if($("#sponser").hasClass("success-message") && 
                           $("#email_error").hasClass("success-message") && 
                           $("#mobile_error").hasClass("success-message")) {
                            $("#submit").removeAttr("disabled");
                        }
                    }
                });
            });

            function check_sponser(refer_id){
                if(!refer_id) {
                    $("#sponser").html("Please enter sponsor ID").addClass("error-message").removeClass("success-message");
                    $("#submit").attr("disabled", "true");
                    return;
                }

                // Show loading indicator
                $("#sponser").html('<i class="fa fa-spinner fa-spin"></i> Verifying...').removeClass("error-message success-message");

                $.get("../lib/get_availability.php",{'action':'sponsor','refer_id':refer_id},function(data){
                    if(data.invalid){
                        $("#submit").attr("disabled", "true");
                        $("#sponser").html('<i class="fa fa-times-circle"></i> Invalid sponsor ID').addClass("error-message").removeClass("success-message");
                    }
                    else{
                        $("#sponser").html('<i class="fa fa-check-circle"></i> ' + data.name + " - Valid sponsor ID").addClass("success-message").removeClass("error-message");
                        checkAllValidations();
                    }
                },"json");
            }

            function check_mobile(mobile){
                if(!mobile) {
                    $("#mobile_error").html("Please enter mobile number").addClass("error-message").removeClass("success-message");
                    $("#submit").attr("disabled", "true");
                    return;
                }

                pattern = /^[0-9]+$/;

                // Show loading indicator
                $("#mobile_error").html('<i class="fa fa-spinner fa-spin"></i> Verifying...').removeClass("error-message success-message");

                $.get("../lib/get_availability.php",{'action':'mobile','mobile':mobile},function(data){
                    console.log(data);
                    if(!data.invalid){
                        $("#submit").attr("disabled", "true");
                        $("#mobile_error").html('<i class="fa fa-times-circle"></i> Mobile number already exists').addClass("error-message").removeClass("success-message");
                    }
                    else if(mobile.length != 10){
                        $("#submit").attr("disabled", "true");
                        $("#mobile_error").html('<i class="fa fa-info-circle"></i> Mobile must be 10 digits').addClass("error-message").removeClass("success-message");
                    }
                    else if(!pattern.test(mobile)){
                        $("#submit").attr("disabled", "true");
                        $("#mobile_error").html('<i class="fa fa-info-circle"></i> Mobile must contain only numbers').addClass("error-message").removeClass("success-message");
                    }
                    else{
                        $("#mobile_error").html('<i class="fa fa-check-circle"></i> Valid mobile number').addClass("success-message").removeClass("error-message");
                        checkAllValidations();
                    }
                },"json");
            }

            function check_email(email){
                if(!email) {
                    $("#email_error").html("Please enter email address").addClass("error-message").removeClass("success-message");
                    $("#submit").attr("disabled", "true");
                    return;
                }

                pattern = /^([a-zA-Z0-9_.-])+@([a-zA-Z0-9_.-])+\.([a-zA-Z])+([a-zA-Z])+/;

                // Show loading indicator
                $("#email_error").html('<i class="fa fa-spinner fa-spin"></i> Verifying...').removeClass("error-message success-message");

                $.get("../lib/get_availability.php",{'action':'email','email':email},function(data){
                    if(!data.invalid){
                        $("#submit").attr("disabled", "true");
                        $("#email_error").html('<i class="fa fa-times-circle"></i> Email already exists').addClass("error-message").removeClass("success-message");
                    }
                    else if(!pattern.test(email)){
                        $("#submit").attr("disabled", "true");
                        $("#email_error").html('<i class="fa fa-info-circle"></i> Invalid email format').addClass("error-message").removeClass("success-message");
                    }
                    else{
                        $("#email_error").html('<i class="fa fa-check-circle"></i> Valid email address').addClass("success-message").removeClass("error-message");
                        checkAllValidations();
                    }
                },"json");
            }

            function checkAllValidations() {
                if($("#sponser").hasClass("success-message") && 
                   $("#email_error").hasClass("success-message") && 
                   $("#mobile_error").hasClass("success-message") &&
                   ($("#password").val() === $("#confirm_password").val()) && 
                   $("#confirm_password").val() !== "") {
                    $("#submit").removeAttr("disabled");
                }
            }

            function otp_email(){
                let email = $("#email").val();
                pattern = /^([a-zA-Z0-9_.-])+@([a-zA-Z0-9_.-])+\.([a-zA-Z])+([a-zA-Z])+/;

                if(!email) {
                    $("#email_error").html('<i class="fa fa-info-circle"></i> Please enter email address').addClass("error-message").removeClass("success-message");
                    $("#submit").attr("disabled", "true");
                    return;
                }

                if(!pattern.test(email)){
                    $("#submit").attr("disabled", "true");
                    $("#email_error").html('<i class="fa fa-info-circle"></i> Invalid email format').addClass("error-message").removeClass("success-message");
                }
                else{
                    // Show loading indicator
                    $("#email_error").html('<i class="fa fa-spinner fa-spin"></i> Sending OTP...').removeClass("error-message success-message");

                    $.get("register.php",{'type':'register','email':email},function(data){
                        if(data._status !== 1){
                            $("#submit").attr("disabled", "true");
                            $("#email_error").html('<i class="fa fa-times-circle"></i> ' + data.msg).addClass("error-message").removeClass("success-message");
                        }
                        else{
                            $("#email_error").html('<i class="fa fa-check-circle"></i> ' + data.msg).addClass("success-message").removeClass("error-message");
                            $("#otp").focus();
                        }
                    },"json");
                }
            }

            // Country search functionality
            $(document).ready(function() {
                $("#country_input").on("keyup focus click", function() {
                    var value = $(this).val().toLowerCase();
                    var countryList = $("#country_list");

                    // Show the dropdown
                    countryList.show();

                    // Filter countries based on input
                    $(".country-dropdown-item").each(function() {
                        var text = $(this).text().toLowerCase();
                        var matches = text.indexOf(value) > -1;
                        $(this).toggle(matches);
                    });

                    // If no matches found, show a message
                    if ($(".country-dropdown-item:visible").length === 0) {
                        if (value.length > 0) {
                            if ($("#no-results").length === 0) {
                                countryList.append('<div id="no-results" class="p-2 text-center">No matching countries found</div>');
                            }
                        } else {
                            $("#no-results").remove();
                        }
                    } else {
                        $("#no-results").remove();
                    }
                });

                // Handle country selection
                $(document).on("click", ".country-dropdown-item", function() {
                    var countryId = $(this).data("id");
                    var countryName = $(this).data("name");
                    var countryCode = $(this).data("code");

                    $("#country_input").val(countryName + " (+" + countryCode + ")");
                    $("#country").val(countryId);
                    $("#country_list").hide();
                    $("#country_error").html('<i class="fa fa-check-circle"></i> Country selected').addClass("country-success").removeClass("country-error");

                    // Move focus to next field
                    $("#mobile").focus();
                });

                // Hide dropdown when clicking outside
                $(document).on("click", function(e) {
                    if (!$(e.target).closest(".form-group").length ||
                        ($(e.target).closest(".form-group").length && !$(e.target).is("#country_input") && !$(e.target).closest("#country_list").length)) {
                        $("#country_list").hide();
                    }
                });

                // Validate country on blur
                $("#country_input").on("blur", function() {
                    setTimeout(function() {
                        if (!$("#country").val()) {
                            var value = $("#country_input").val();
                            if (value) {
                                $("#country_error").html('<i class="fa fa-info-circle"></i> Please select a country from the list').addClass("country-error").removeClass("country-success");
                            }
                        }
                    }, 200);
                });
            });
        </script>
    </body>
</html>
