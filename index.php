<?php include '../lib/config.php';
if(SITE_WORKING_STATUS){
    echo '<center style="position: relative; top: 100px;"><h1>This site is under maintenance</h1></center>';die;
}
if(isset($_SESSION['userid']) && !empty($_SESSION['userid'])){redirect('./dashboard.php');}
//redirect('../');die;
?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
    <title>Member Login</title>
    <link rel="shortcut icon" href="images/nexabot-logo.png" type="image/x-icon">
    <script src="https://ajax.googleapis.com/ajax/libs/webfont/1.6.26/webfont.js"></script>
    <script>
    WebFont.load({
        google: {
            families: ['Alegreya+Sans:100,100i,300,300i,400,400i,500,500i,700,700i,800,800i,900,900i', 'Raleway:100,100i,200,200i,300,300i,400,400i,500,500i,600,600i,700,700i,800,800i,900,900i', 'Open Sans']
        }
    });
    </script>
    <!-- Bootstrap -->
    <link href="../assets/bootstrap/css/bootstrap.min.css" rel="stylesheet" type="text/css" />
    <!-- Bootstrap rtl -->
    <!--<link href="../assets/bootstrap-rtl/bootstrap-rtl.min.css" rel="stylesheet" type="text/css"/>-->
    <!-- Pe-icon-7-stroke -->
    <link href="../assets/pe-icon-7-stroke/css/pe-icon-7-stroke.css" rel="stylesheet" type="text/css" />
    <link href="../assets/font-awesome/css/font-awesome.min.css" rel="stylesheet" type="text/css" />
    <!-- Theme style -->
    <link href="../assets/dist/css/component_ui.css" rel="stylesheet" type="text/css" />
    <link href="../assets/dist/css/skins/component_ui_black.css" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.8.2/css/all.css" integrity="sha384-oS3vJWv+0UjzBfQzYUhtDYW+Pj2yciDJxpsK1OYPAYjqT085Qq/1cq5FLXAZQ7Ay" crossorigin="anonymous">
    <!-- Theme style rtl -->
    <!--<link href="../assets/dist/css/component_ui_rtl.css" rel="stylesheet" type="text/css"/>-->
    <!-- Custom css -->
    <link href="../assets/dist/css/custom.css" rel="stylesheet" type="text/css" />
        <style>
            * {
                margin: 0;
                padding: 0;
                box-sizing: border-box;
            }

            body {
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
                background-color: #0b0e11;
                color: #e6edf3;
                overflow-x: hidden;
                min-height: 100vh;
                display: flex;
                align-items: center;
                justify-content: center;
                padding: 10px;
            }

            .main-wrapper {
                width: 100%;
                max-width: 1200px;
                height: 90vh;
                background: #1a1f2e;
                border-radius: 20px;
                overflow: hidden;
                box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
                position: relative;
            }

            .main-container {
                display: flex;
                height: 100%;
            }

            /* Left Sidebar */
            .sidebar {
                flex: 1;
                background: linear-gradient(135deg, #00cc88 0%, #0099ff 100%);
                padding: 40px;
                display: flex;
                flex-direction: column;
                justify-content: space-between;
                position: relative;
                overflow: hidden;
            }

            .sidebar::before {
                content: '';
                position: absolute;
                top: -50%;
                left: -50%;
                width: 200%;
                height: 200%;
                background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
                animation: rotate 20s linear infinite;
            }

            @keyframes rotate {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }

            .sidebar-content {
                position: relative;
                z-index: 2;
                flex: 1;
                display: flex;
                flex-direction: column;
                justify-content: center;
            }

            .feature-item {
                display: flex;
                align-items: center;
                margin-bottom: 30px;
                opacity: 0;
                animation: slideInLeft 0.8s ease forwards;
            }

            .feature-item:nth-child(1) { animation-delay: 0.2s; }
            .feature-item:nth-child(2) { animation-delay: 0.4s; }
            .feature-item:nth-child(3) { animation-delay: 0.6s; }
            .feature-item:nth-child(4) { animation-delay: 0.8s; }

            @keyframes slideInLeft {
                from {
                    opacity: 0;
                    transform: translateX(-30px);
                }
                to {
                    opacity: 1;
                    transform: translateX(0);
                }
            }

            .feature-icon {
                width: 50px;
                height: 50px;
                background: rgba(255, 255, 255, 0.2);
                border-radius: 12px;
                display: flex;
                align-items: center;
                justify-content: center;
                margin-right: 20px;
                font-size: 20px;
                color: white;
                backdrop-filter: blur(10px);
                border: 1px solid rgba(255, 255, 255, 0.1);
                transition: all 0.3s ease;
            }

            .feature-icon:hover {
                background: rgba(255, 255, 255, 0.3);
                transform: scale(1.1);
            }

            .feature-text {
                color: white;
                font-size: 16px;
                font-weight: 500;
                line-height: 1.4;
            }

            /* Coins Container */
            .coins-container {
                position: relative;
                z-index: 2;
                margin-top: 40px;
                text-align: center;
            }

            .coins-image {
                max-width: 100%;
                height: auto;
                max-height: 200px;
                filter: drop-shadow(0 10px 20px rgba(0, 0, 0, 0.3));
                animation: float 3s ease-in-out infinite;
            }

            @keyframes float {
                0%, 100% { transform: translateY(0px); }
                50% { transform: translateY(-10px); }
            }

            /* Crystal Effect */
            .crystal {
                position: absolute;
                top: 20%;
                right: 10%;
                width: 60px;
                height: 60px;
                background: linear-gradient(45deg, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.1));
                border-radius: 50% 10% 50% 10%;
                transform: rotate(45deg);
                animation: sparkle 4s ease-in-out infinite;
            }

            @keyframes sparkle {
                0%, 100% { opacity: 0.3; transform: rotate(45deg) scale(1); }
                50% { opacity: 0.8; transform: rotate(45deg) scale(1.2); }
            }

            /* Right Form Container */
            .form-container {
                flex: 1;
                background-color: #1a1f2e;
                padding: 40px;
                /*display: flex;*/
                /*flex-direction: row;*/
                /*justify-content: center;*/
                position: relative;
            }

            .form-title {
                font-size: 32px;
                font-weight: 700;
                color: #e6edf3;
                margin-bottom: 30px;
                text-align: left;
            }

            .form-group {
                margin-bottom: 20px;
                position: relative;
            }

            .form-label {
                display: block;
                margin-bottom: 8px;
                color: #94a3b8;
                font-size: 14px;
                font-weight: 500;
            }

            .required::after {
                content: ' *';
                color: #ef4444;
            }

            .form-control {
                width: 100%;
                padding: 15px 15px 15px 45px;
                background: #2a3441;
                border: 1px solid #3a4553;
                border-radius: 10px;
                color: #e6edf3;
                font-size: 16px;
                transition: all 0.3s ease;
                outline: none;
            }

            .form-control:focus {
                border-color: #00cc88;
                box-shadow: 0 0 0 3px rgba(0, 204, 136, 0.1);
                background: #323b4a;
            }

            .form-control::placeholder {
                color: #64748b;
            }

            .form-icon {
                position: absolute;
                left: 15px;
                top: 38px;
                color: #64748b;
                font-size: 16px;
                transition: all 0.3s ease;
            }

            .form-control:focus + .form-icon {
                color: #00cc88;
            }

            .btn-signin {
                width: 100%;
                padding: 16px;
                background: linear-gradient(135deg, #00cc88 0%, #0099ff 100%);
                border: none;
                border-radius: 10px;
                color: white;
                font-size: 16px;
                font-weight: 600;
                cursor: pointer;
                transition: all 0.3s ease;
                margin-bottom: 20px;
                display: flex;
                align-items: center;
                justify-content: center;
                gap: 10px;
            }

            .btn-signin:hover {
                transform: translateY(-2px);
                box-shadow: 0 10px 25px rgba(0, 204, 136, 0.3);
            }

            .forgot-password {
                text-align: center;
                margin-bottom: 20px;
            }

            .forgot-password a {
                color: #0099ff;
                text-decoration: none;
                font-size: 14px;
                transition: all 0.3s ease;
            }

            .forgot-password a:hover {
                color: #00cc88;
                text-decoration: underline;
            }

            .register-link {
                text-align: center;
                color: #94a3b8;
                font-size: 14px;
            }

            .register-link a {
                color: #0099ff;
                text-decoration: none;
                font-weight: 600;
                transition: all 0.3s ease;
            }

            .register-link a:hover {
                color: #00cc88;
                text-decoration: underline;
            }

            /* Notification Styles */
            .notification {
                padding: 12px 16px;
                border-radius: 8px;
                margin-bottom: 20px;
                font-size: 14px;
                font-weight: 500;
            }

            .notification.success {
                background: rgba(0, 204, 136, 0.1);
                border: 1px solid rgba(0, 204, 136, 0.3);
                color: #00cc88;
            }

            .notification.error {
                background: rgba(239, 68, 68, 0.1);
                border: 1px solid rgba(239, 68, 68, 0.3);
                color: #ef4444;
            }

            /* Responsive */
            @media (max-width: 768px) {
                body {
                    padding: 0;
                    margin: 0;
                }

                .main-wrapper {
                    width: 100%;
                    max-width: 100%;
                    height: 100vh;
                    border-radius: 0;
                    display: flex;
                    flex-direction: column;
                    box-shadow: none;
                }

                .main-container {
                    flex-direction: column;
                    flex: 1;
                    display: flex;
                    width: 100%;
                }

                /* Form container - Always first in mobile */
                .form-container {
                    order: 1;
                    flex: 1;
                    width: 100%;
                    padding: 20px;
                    background-color: #1a1f2e;
                    display: flex;
                    flex-direction: column;
                    justify-content: flex-start;
                    overflow-y: auto;
                    min-height: 60vh;
                    box-sizing: border-box;
                }

                /* Sidebar - Always second in mobile */
                .sidebar {
                    order: 2;
                    flex: 0 0 auto;
                    width: 100%;
                    min-height: auto;
                    padding: 20px;
                    background: linear-gradient(135deg, #00cc88 0%, #0099ff 100%);
                    display: flex;
                    flex-direction: column;
                    box-sizing: border-box;
                }

                .sidebar-content {
                    flex: 1;
                    display: flex;
                    flex-direction: column;
                    justify-content: flex-start;
                    width: 100%;
                }

                .feature-item {
                    margin-bottom: 12px;
                    display: flex;
                    align-items: center;
                    width: 100%;
                }

                .feature-icon {
                    width: 30px;
                    height: 30px;
                    font-size: 14px;
                    margin-right: 10px;
                    flex-shrink: 0;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    background: rgba(255, 255, 255, 0.2);
                    border-radius: 8px;
                }

                .feature-text {
                    font-size: 13px;
                    line-height: 1.2;
                    flex: 1;
                    width: 100%;
                }

                .coins-container {
                    margin-top: 15px;
                    display: none;
                }

                .crystal {
                    display: none;
                }

                .form-title {
                    font-size: 24px;
                    margin-bottom: 20px;
                    text-align: center;
                    width: 100%;
                }

                .form-group {
                    margin-bottom: 16px;
                    width: 100%;
                }

                .form-control {
                    width: 100%;
                    padding: 14px 15px 14px 40px;
                    font-size: 16px;
                    box-sizing: border-box;
                }

                .form-icon {
                    top: 36px;
                    font-size: 16px;
                }

                .btn-signin {
                    width: 100%;
                    padding: 16px;
                    font-size: 16px;
                    margin-bottom: 20px;
                    box-sizing: border-box;
                }

                .forgot-password {
                    font-size: 14px;
                    margin-bottom: 15px;
                    width: 100%;
                    text-align: center;
                }

                .register-link {
                    font-size: 14px;
                    margin-bottom: 20px;
                    width: 100%;
                    text-align: center;
                }

                /* Form elements full width */
                form {
                    width: 100%;
                }

                .form-label {
                    width: 100%;
                }

                .notification {
                    width: 100%;
                    box-sizing: border-box;
                }
            }

            /* Extra small devices (phones, 600px and down) */
            @media (max-width: 600px) {
                .form-container {
                    padding: 15px;
                }

                .sidebar {
                    padding: 15px;
                }

                .form-title {
                    font-size: 20px;
                    margin-bottom: 15px;
                }

                .form-control {
                    padding: 12px 15px 12px 35px;
                    font-size: 14px;
                }

                .btn-signin {
                    padding: 14px;
                    font-size: 14px;
                }

                .feature-text {
                    font-size: 12px;
                }
            }
        </style>
</head>

    <body>
        <div class="main-wrapper">
            <div class="main-container">
                <!-- Left Sidebar -->
                <div class="sidebar">
                    <div class="sidebar-content">
                        <div class="feature-item">
                            <div class="feature-icon">
                                <i class="fa fa-gamepad"></i>
                            </div>
                            <div class="feature-text">
                                Exciting and fair games<br>and lotteries
                            </div>
                        </div>

                        <div class="feature-item">
                            <div class="feature-icon">
                                <i class="fa fa-line-chart"></i>
                            </div>
                            <div class="feature-text">
                                Staking offers with<br>returns of up to 200%
                            </div>
                        </div>

                        <div class="feature-item">
                            <div class="feature-icon">
                                <i class="fa fa-shield"></i>
                            </div>
                            <div class="feature-text">
                                We ensure your security<br>and anonymity
                            </div>
                        </div>

                        <div class="feature-item">
                            <div class="feature-icon">
                                <i class="fa fa-users"></i>
                            </div>
                            <div class="feature-text">
                                Join our generous<br>affiliate program
                            </div>
                        </div>
                    </div>

                    <!-- Coin Graphics -->
                    <div class="coins-container">
                        <img src="https://i.ibb.co/pNQMJ43/coins.png" alt="Coins" class="coins-image">
                    </div>

                    <!-- Crystal -->
                    <div class="crystal"></div>
                </div>

                <!-- Right Form Container -->
                <div class="form-container">
                    <h1 class="form-title">Sign In</h1>

                    <?php if(isset($_SESSION['SetMessage'])): ?>
                    <div class="notification <?php echo strpos($_SESSION['SetMessage'], 'danger') !== false ? 'error' : 'success'; ?>">
                        <?php echo getMessage(); ?>
                    </div>
                    <?php endif; ?>

                    <form action="login_model.php" id="loginForm" method="post">
                        <div class="form-group">
                            <label for="login_id" class="form-label required">User Id</label>
                            <input type="text" class="form-control" id="login_id" name="login_id" placeholder="Enter your user id" maxlength="20" required />
                            <i class="form-icon fa fa-user"></i>
                        </div>

                        <div class="form-group">
                            <label for="password" class="form-label required">Password</label>
                            <input type="password" class="form-control" id="password" name="password" placeholder="Enter your password" maxlength="20" required />
                            <i class="form-icon fa fa-lock"></i>
                        </div>

                        <div class="forgot-password">
                            <a href="forgot.php">Forgot password</a>
                        </div>

                        <button class="btn-signin" type="submit">
                            Sign In
                            <i class="fa fa-arrow-right"></i>
                        </button>

                        <div class="register-link">
                            Don't have an account? <a href="register.php">Register an account</a>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- jQuery -->
        <script src="../assets/plugins/jQuery/jquery-1.12.4.min.js"></script>
        <script>
            // Form validation and animations
            $(document).ready(function(){
                // Add focus animations
                $('.form-control').on('focus', function() {
                    $(this).parent().addClass('focused');
                });

                $('.form-control').on('blur', function() {
                    if ($(this).val() === '') {
                        $(this).parent().removeClass('focused');
                    }
                });

                // Form submission
                $('#loginForm').on('submit', function(e) {
                    const submitBtn = $('.btn-signin');
                    submitBtn.html('<i class="fa fa-spinner fa-spin"></i> Signing In...');
                    submitBtn.prop('disabled', true);
                });
            });
        </script>
    </body>
</html>